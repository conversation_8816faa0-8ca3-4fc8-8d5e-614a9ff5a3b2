<p align="center">
  <a href="https://proxifly.dev">
    <img src="https://cdn.itwcreativeworks.com/assets/proxifly/images/logo/proxifly-brandmark-black-x.svg" width="100px">
  </a>
</p>

<p align="center">
  <img src="https://img.shields.io/badge/Updated_Every_5_Minutes-passing-success">
  <br>
  <a href="https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/all/data.txt">
    <img src="https://img.shields.io/badge/all-11668-blue">
  </a>
  <a href="https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/http/data.txt">
    <img src="https://img.shields.io/badge/http-11470-blue">
  </a>
  <a href="https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/socks4/data.txt">
    <img src="https://img.shields.io/badge/socks4-149-blue">
  </a>
  <a href="https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/socks5/data.txt">
    <img src="https://img.shields.io/badge/socks5-45-blue">
  </a>
  <br>
  <!-- <img src="https://img.shields.io/librariesio/release/npm/node-powertools.svg"> -->
  <!-- <img src="https://img.shields.io/bundlephobia/min/node-powertools.svg"> -->
  <!-- <img src="https://img.shields.io/codeclimate/maintainability-percentage/proxifly/free-proxy-list.svg"> -->
  <!-- <img src="https://img.shields.io/npm/dm/node-powertools.svg"> -->
  <!-- <img src="https://img.shields.io/node/v/node-powertools.svg"> -->
  <img src="https://img.shields.io/website/https/proxifly.dev.svg">
  <!-- <img src="https://img.shields.io/github/contributors/proxifly/free-proxy-list.svg"> -->
  <img src="https://img.shields.io/github/last-commit/proxifly/free-proxy-list.svg">
  <img src="https://img.shields.io/github/license/proxifly/free-proxy-list.svg">
  <br>
  <br>
  <a href="https://proxifly.dev">Site</a> | <a href="https://www.npmjs.com/package/proxifly">NPM Module</a> | <a href="https://github.com/proxifly/free-proxy-list">GitHub Repo</a>
</p>

# 🌎 Proxifly's Free Proxy List
Every 5 minutes, **Proxifly** fetches fresh proxies—including **HTTP**, **HTTPS**, **SOCKS4**, and **SOCKS5** proxies—from around the web.

> Proxifly found **11668** working proxies from **50** countries in the latest update (**May 29, 2025, 06:51 PM UTC**).

## 🦄 Features
* ⚡ Extremely fast
* 📝 Validated every 5 minutes
* 📓 Sorted into **HTTP**, **HTTPS**, **SOCKS4**, & **SOCKS5**
* 🌎 Contains proxies from **50 countries**
* 📦 Available in **.json**, **.txt**, & **.csv** formats
* 🔐 Supports HTTPS connection
* 😊 No duplicates

### 🛑 Please follow the [GitHub Acceptable Use Policy](https://docs.github.com/en/site-policy/acceptable-use-policies/github-acceptable-use-policies) when using this project. You should use these proxies responsibly, without abusing them, and without intent to commit illegal activity.

## 📦 Usage
There are so many ways to get our free proxies.

You can just download the whole list as a `.txt` file, or you can even use the **Proxifly NPM module** to fetch proxies into your application programatically.

Proxifly sorts the proxies by protocol and country, but you can also just get the whole unsorted list too.


### 👑 Download From Our Website
Scrape directly from our website's [free proxy list](https://proxifly.dev/tools/proxy-list/).


### 💎 Download in our Scraping Software
Get proxies with our free [proxy scraper software](https://proxifly.dev/download).

[![Windows](https://img.shields.io/badge/-Windows_x64-blue.svg?style=for-the-badge&logo=windows)](https://proxifly.dev/download?download=windows)
[![MacOS](https://img.shields.io/badge/-MacOS-lightblue.svg?style=for-the-badge&logo=apple)](https://proxifly.dev/download?download=macos)
[![Unix](https://img.shields.io/badge/-Linux/BSD-red.svg?style=for-the-badge&logo=linux)](https://proxifly.dev/download?download=linux)
[![All versions](https://img.shields.io/badge/-All_Versions-lightgrey.svg?style=for-the-badge)](https://proxifly.dev/download?download=null)


### 🔗 Direct Download Links
Click on your preferred file format to get the updated list

|Type|Count|.json|.txt|.csv|
|----|-----|-----|----|----|
|All Proxies|11668|[JSON File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/all/data.json)|[Text File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/all/data.txt)|[CSV File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/all/data.csv)|
|HTTP Proxies|11470|[JSON File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/http/data.json)|[Text File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/http/data.txt)|[CSV File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/http/data.csv)|
|HTTPS Proxies|4|[JSON File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/https/data.json)|[Text File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/https/data.txt)|[CSV File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/https/data.csv)|
|SOCKS4 Proxies|149|[JSON File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/socks4/data.json)|[Text File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/socks4/data.txt)|[CSV File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/socks4/data.csv)|
|SOCKS5 Proxies|45|[JSON File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/socks5/data.json)|[Text File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/socks5/data.txt)|[CSV File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/socks5/data.csv)|
|U.S. Proxies ([View More Countries](https://github.com/proxifly/free-proxy-list/tree/main/proxies/countries))|4950|[JSON File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/US/data.json)|[Text File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/US/data.txt)|[CSV File](https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/US/data.csv)|


#### Other Sorted Proxies
* [🌎 Get proxies by country](https://github.com/proxifly/free-proxy-list/tree/main/proxies/countries)

### 🙌 Use the Proxifly NPM Module
Easily fetch updated proxies in your application with the official **Proxifly NPM module**.

```shell
npm install proxifly
```

```js
const proxifly = new (require('proxifly'))({
  // Not required, but having one removes limits (get your key at https://proxifly.dev).
  apiKey: 'api_test_key'
});
```

```js
var options = {
  protocol: 'http', // http | socks4 | socks5
  anonymity: 'elite', // transparent | anonymous | elite
  country: 'US', // https://www.nationsonline.org/oneworld/country_code_list.htm
  https: true, // true | false
  speed: 10000, // 0 - 60000
  format: 'json', // json | text
  quantity: 1, // 1 - 20
};

proxifly.getProxy(options)
.then(proxy => {
  console.log('Proxies:', proxy);
})
.catch(e => {
  console.error(e);
})
```
**options**
The options for getProxy(options) are as follows.

- protocol string, array (optional): Filter by the protocol
  - Values: http, socks4, socks5
  - Default: http
- anonymity string, array (optional): Filter by anonymity level.
  - Values: transparent, anonymous, elite (elite is the most anonymous)
  - Default: null (no filter, any anonymity)
- country string, array (optional): Filter by country.
  - Values: US, CA, RU... (see full list at https://www.nationsonline.org/oneworld/country_code_list.htm)
  - Default: null (no filter, any country)
- https boolean (optional): Filter by https/SSL.
  - Values: true, false
  - Default: null (no filter, any level of https)
- speed number (optional): Filter by speed, value is in milliseconds taken to connect.
  - Values: 0 - 60000
  - Default: null (no filter, any speed)
  - Note: Specifying a very low number (less than ~400) will return significantly fewer results
- format string (optional): The response type.
  - Values: json, text
  - Default: json
- quantity number (optional): The number of proxies to be returned. Any number greater than 1 will be returned in an array.
  - Values: 1 - 20
  - Default: 1
  - Note: Without an API key, you cannot return more than 1 result.

For most options like protocol, anonymity, and country, you can provide an array where each element in the array will act as OR logic. For example:

```js
var options = {
  protocol: ['http', 'socks4'],
  anonymity: ['anonymous', 'elite'],
  country: ['US', 'GB', 'RU'],
}
```
This filter will call the API for any proxies that are either of protocol (http OR socks4) AND of anonymity (anonymous OR elite) AND from (US OR GB OR RU)!

**Example output**
Here is a sample response for the .getProxy() method. This is the output you will see when extended=true:

```json
{
  proxy: 'socks4://**************:5678',
  protocol: 'socks4',
  ip: '**************',
  port: 5678,
  https: false,
  anonymity: 'transparent',
  score: 1,
  geolocation: { country: 'IN', city: 'Unknown' }
}
```

**getPublicIp()**
Get your public IP with a simple api call.

```js
var options = {
  mode: 'IPv4', // IPv4 | IPv6
  format: 'json', // json | text
};

proxifly.getPublicIp(options)
.then(proxy => {
  console.log('getPublicIp:', response);
  console.log('My IP is:', response.ip);
  console.log('My country is:', response.country);
})
.catch(e => {
  console.error(e);
})
```

**options**

The options for getProxy(options) are as follows.

- mode string (optional): IPv4 IP or IPv6 IP?
  - Values: IPv4, IPv6
  - Default: IPv4
- format string (optional): The response type.
  - Values: json, text
  - Default: json

**Example output**
Here is a sample response for the .getPublicIp() method. This is the output you will see when extended=true:

```json
{
  ip: '***************',
  geolocation: {
    country: 'US',
    city: 'Los Angeles',
    latitude: 34.0544,
    longitude: -118.2441
  }
}
```
For a more in-depth documentation of this library and the Proxifly service, please visit the official Proxifly website.


### 🔑 Fetch with cURL
Fetch the latest proxy list with the following command:


#### All Proxies
```shell
curl -sL https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/all/data.txt -o all.txt
```


#### HTTP Proxies
```shell
curl -sL https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/http/data.txt -o http.txt
```


#### SOCKS4 Proxies
```shell
curl -sL https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/socks4/data.txt -o socks4.txt
```


#### SOCKS5 Proxies
```shell
curl -sL https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/socks5/data.txt -o socks5.txt
```

#### U.S. Proxies ([View More Countries](https://github.com/proxifly/free-proxy-list/tree/main/proxies/countries))
```shell
curl -sL https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/US/data.txt -o socks5.txt
```

## 🧸 Contributing
Contributions are welcome, and they are greatly appreciated! Every
little bit helps, and credit will always be given.

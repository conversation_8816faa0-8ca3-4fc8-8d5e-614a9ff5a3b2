const proxifly = require('proxifly');
const axios = require('axios');
const fs = require('fs');

class ProxyManager {
    constructor() {
        this.proxies = [];
        this.workingProxies = [];
        this.lastUpdate = 0;
        this.updateInterval = 5 * 60 * 1000; // 5 Minuten
        this.proxiflyClient = new proxifly({
            // Kein API-Key erforderlich für grundlegende Nutzung
        });

        // Backup der alten Proxy-Konfiguration
        this.backupOldProxyConfig();

        // Initialer Proxy-Load
        this.updateProxies();

        // Automatische Aktualisierung alle 5 Minuten
        setInterval(() => {
            this.updateProxies();
        }, this.updateInterval);
    }

    backupOldProxyConfig() {
        try {
            // Erstelle proxy_archiv Verzeichnis falls es nicht existiert
            if (!fs.existsSync('./proxy_archiv')) {
                fs.mkdirSync('./proxy_archiv', { recursive: true });
            }

            if (fs.existsSync('./proxys.txt')) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                fs.copyFileSync('./proxys.txt', `./proxy_archiv/proxys_backup_${timestamp}.txt`);
                console.log('✓ Alte Proxy-Konfiguration gesichert');
            }
            if (fs.existsSync('./proxy_auth.txt')) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                fs.copyFileSync('./proxy_auth.txt', `./proxy_archiv/proxy_auth_backup_${timestamp}.txt`);
                console.log('✓ Alte Proxy-Authentifizierung gesichert');
            }
        } catch (error) {
            console.log('Warnung: Konnte alte Proxy-Konfiguration nicht sichern:', error.message);
        }
    }

    async fetchProxiesFromCountry(country, protocol = 'http') {
        try {
            const options = {
                protocol: protocol,
                country: country,
                anonymity: ['anonymous', 'elite'], // Nur anonyme Proxys
                https: true,
                speed: 10000, // Max 10 Sekunden Antwortzeit
                format: 'json',
                quantity: 1
            };

            const proxy = await this.proxiflyClient.getProxy(options);
            return proxy;
        } catch (error) {
            console.log(`Fehler beim Abrufen von ${country} Proxys:`, error.message);
            return null;
        }
    }

    async fetchProxiesFromDirectURL(country) {
        try {
            const url = `https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/${country}/data.json`;
            const response = await axios.get(url, { timeout: 10000 });

            if (response.data && Array.isArray(response.data)) {
                // Filtere nur HTTP/HTTPS Proxys mit guter Anonymität
                return response.data.filter(proxy =>
                    (proxy.protocol === 'http' || proxy.protocol === 'https') &&
                    (proxy.anonymity === 'anonymous' || proxy.anonymity === 'elite') &&
                    proxy.speed < 10000
                );
            }
            return [];
        } catch (error) {
            console.log(`Fehler beim direkten Abrufen von ${country} Proxys:`, error.message);
            return [];
        }
    }

    async testProxy(proxy) {
        try {
            // Verwende verschiedene Test-URLs
            const testUrls = [
                'http://httpbin.org/ip',
                'http://icanhazip.com',
                'http://ipinfo.io/ip'
            ];

            for (const testUrl of testUrls) {
                try {
                    const response = await axios.get(testUrl, {
                        proxy: {
                            host: proxy.ip,
                            port: proxy.port,
                            protocol: 'http'
                        },
                        timeout: 5000,
                        headers: {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        }
                    });

                    if (response.status === 200) {
                        console.log(`✓ Proxy ${proxy.ip}:${proxy.port} funktioniert`);
                        return true;
                    }
                } catch (testError) {
                    // Versuche nächste URL
                    continue;
                }
            }

            return false;
        } catch (error) {
            console.log(`✗ Proxy ${proxy.ip}:${proxy.port} fehlgeschlagen: ${error.message}`);
            return false;
        }
    }

    async updateProxies() {
        console.log('\n🔄 Aktualisiere Proxy-Liste...');
        const countries = ['DE', 'AT', 'CH']; // Deutschland, Österreich, Schweiz
        const newProxies = [];

        for (const country of countries) {
            console.log(`📡 Lade Proxys aus ${country}...`);

            // Verwende direkten Download als Hauptmethode
            try {
                const directProxies = await this.fetchProxiesFromDirectURL(country);
                if (directProxies.length > 0) {
                    // Nimm die ersten 10 besten Proxys
                    newProxies.push(...directProxies.slice(0, 10));
                    console.log(`✓ ${directProxies.length} Proxys aus ${country} gefunden`);
                }
            } catch (error) {
                console.log(`⚠️ Direkter Download für ${country} fehlgeschlagen: ${error.message}`);
            }

            // Kurze Pause zwischen Ländern
            await this.sleep(1000);
        }

        // Fallback: Lade alle HTTP Proxys wenn keine länderspezifischen gefunden
        if (newProxies.length === 0) {
            console.log('🔄 Lade allgemeine HTTP Proxys als Fallback...');

            // Versuche verschiedene Proxy-Quellen
            const proxySources = [
                'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/http/data.json',
                'https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt',
                'https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt'
            ];

            for (const source of proxySources) {
                try {
                    if (source.endsWith('.json')) {
                        const response = await axios.get(source, { timeout: 10000 });
                        if (response.data && Array.isArray(response.data)) {
                            const filteredProxies = response.data.filter(proxy =>
                                (proxy.anonymity === 'anonymous' || proxy.anonymity === 'elite') &&
                                proxy.speed < 5000
                            ).slice(0, 15);
                            newProxies.push(...filteredProxies);
                            console.log(`✓ ${filteredProxies.length} Proxys von JSON-Quelle geladen`);
                        }
                    } else {
                        // Text-basierte Proxy-Liste
                        const response = await axios.get(source, { timeout: 10000 });
                        if (response.data) {
                            const proxyLines = response.data.split('\n').filter(line =>
                                line.trim() && line.includes(':') && !line.startsWith('#')
                            ).slice(0, 10);

                            for (const line of proxyLines) {
                                const [ip, port] = line.trim().split(':');
                                if (ip && port && !isNaN(port)) {
                                    newProxies.push({
                                        ip: ip,
                                        port: parseInt(port),
                                        protocol: 'http',
                                        anonymity: 'unknown',
                                        speed: 5000
                                    });
                                }
                            }
                            console.log(`✓ ${proxyLines.length} Proxys von Text-Quelle geladen`);
                        }
                    }

                    if (newProxies.length > 0) break; // Stoppe wenn Proxys gefunden

                } catch (error) {
                    console.log(`⚠️ Quelle ${source} fehlgeschlagen: ${error.message}`);
                }

                await this.sleep(1000);
            }
        }

        console.log(`📊 ${newProxies.length} Proxys gefunden, teste Funktionalität...`);

        // Teste nur die ersten 10 Proxys für bessere Performance
        const proxiesToTest = newProxies.slice(0, 10);
        const workingProxies = [];

        for (const proxy of proxiesToTest) {
            const isWorking = await this.testProxy(proxy);
            if (isWorking) {
                workingProxies.push(proxy);
                // Stoppe wenn wir 5 funktionierende Proxys haben
                if (workingProxies.length >= 5) {
                    console.log('✓ Genügend funktionierende Proxys gefunden, stoppe Tests');
                    break;
                }
            }

            // Kurze Pause zwischen Tests
            await this.sleep(500);
        }

        this.workingProxies = workingProxies;
        this.lastUpdate = Date.now();

        console.log(`✅ ${workingProxies.length} funktionierende Proxys gefunden`);

        // Speichere funktionierende Proxys in Datei
        this.saveProxiesToFile();

        return workingProxies;
    }

    saveProxiesToFile() {
        try {
            const proxyStrings = this.workingProxies.map(proxy => `${proxy.ip}:${proxy.port}`);
            fs.writeFileSync('./proxys.txt', proxyStrings.join('\n'));

            // Erstelle auch eine detaillierte JSON-Datei
            fs.writeFileSync('./proxys_detailed.json', JSON.stringify(this.workingProxies, null, 2));

            console.log(`💾 ${proxyStrings.length} Proxys in proxys.txt gespeichert`);
        } catch (error) {
            console.log('Fehler beim Speichern der Proxys:', error.message);
        }
    }

    getRandomProxy() {
        if (this.workingProxies.length === 0) {
            console.log('⚠️ Keine funktionierenden Proxys verfügbar!');
            return null;
        }

        const randomIndex = Math.floor(Math.random() * this.workingProxies.length);
        const proxy = this.workingProxies[randomIndex];
        return `${proxy.ip}:${proxy.port}`;
    }

    getProxyList() {
        return this.workingProxies.map(proxy => `${proxy.ip}:${proxy.port}`);
    }

    getProxyCount() {
        return this.workingProxies.length;
    }

    getLastUpdateTime() {
        return new Date(this.lastUpdate).toLocaleString('de-DE');
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Manuelle Proxy-Aktualisierung
    async forceUpdate() {
        console.log('🔄 Erzwinge Proxy-Aktualisierung...');
        return await this.updateProxies();
    }

    // Entferne einen defekten Proxy aus der Liste
    removeProxy(proxyString) {
        const [ip, port] = proxyString.split(':');
        this.workingProxies = this.workingProxies.filter(proxy =>
            !(proxy.ip === ip && proxy.port.toString() === port)
        );
        this.saveProxiesToFile();
        console.log(`🗑️ Proxy ${proxyString} aus der Liste entfernt`);
    }

    // Statistiken anzeigen
    getStats() {
        return {
            totalProxies: this.workingProxies.length,
            lastUpdate: this.getLastUpdateTime(),
            countries: [...new Set(this.workingProxies.map(p => p.geolocation?.country))],
            protocols: [...new Set(this.workingProxies.map(p => p.protocol))]
        };
    }
}

module.exports = ProxyManager;


const puppeteerExtra = require('puppeteer-extra').use(require('puppeteer-extra-plugin-stealth')());
const readlineSync = require('readline-sync');
const ProxyManager = require('./proxy-manager');

// Globale Variablen
let proxyManager;
let errorMessages = ['HTTP ERROR', 'Access Denied'];

// Initialisiere Proxy-Manager
function initProxyManager() {
    console.log('🚀 Initialisiere Free Proxy Manager...');
    proxyManager = new ProxyManager();
    console.log('✅ Proxy Manager gestartet');
}

// Legacy-Funktion für Rückwärtskompatibilität
function loadConfig() {
    // Diese Funktion wird nicht mehr benötigt, da der ProxyManager
    // automatisch die Proxys verwaltet
    console.log('ℹ️ Proxy-Konfiguration wird automatisch vom ProxyManager verwaltet');
}

// Hilfsfunktionen
async function sleep(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

async function clickElement(page, selector) {
    const element = await page.$(selector);
    await element.click().catch(error => console.log(error));
}

async function launchBrowser(proxy) {
    return await puppeteerExtra.launch({
        // Verwende System-Chrome/Chromium (entferne executablePath für automatische Erkennung)
        'headless': false,
        'defaultViewport': null,
        'ignoreHTTPSErrors': true,
        'timeout': 60000,
        'args': [
            '--no-first-run',
            '--proxy-server=' + proxy,
            '--no-sandbox',
            '--disable-setuid-sandbox'
        ]
    });
}

// Exportiere Hilfsfunktionen für Handler
module.exports = {
    sleep,
    clickElement,
    launchBrowser,
    get proxyManager() { return proxyManager; },
    get errorMessages() { return errorMessages; }
};

// Website-Handler (nach Exports laden)
const EventimHandler = require('./handlers/eventim');
const EventimLightHandler = require('./handlers/eventim-light');
const MuenchenTicketDEHandler = require('./handlers/muenchenticket-de');
const MuenchenTicketNETHandler = require('./handlers/muenchenticket-net');

async function main() {
    // Initialisiere Proxy-Manager
    initProxyManager();

    // Warte kurz, damit der ProxyManager Proxys laden kann
    console.log('⏳ Warte auf initiale Proxy-Liste...');
    await sleep(10); // 10 Sekunden warten

    console.log(`
    Contact Me
    ¨¨¨¨¨¨¨¨¨¨
Whatsapp : +212 622 056197
Email    : <EMAIL>

`);

    console.log(`
1 : eventim.de
2 : eventim-light.com
3 : muenchenticket.de/tickets
4 : muenchenticket.net/shop`);

    let websiteChoice = null;

    while (true) {
        websiteChoice = parseInt(readlineSync.question('\nPlease enter the website number and hit enter: '));

        if (!isNaN(websiteChoice)) {
            switch (websiteChoice) {
                case 1:
                    await EventimHandler.run();
                    break;
                case 2:
                    await EventimLightHandler.run();
                    break;
                case 3:
                    await MuenchenTicketDEHandler.run();
                    break;
                case 4:
                    await MuenchenTicketNETHandler.run();
                    break;
                default:
                    console.log('Number not found');
                    continue;
            }
            break;
        } else {
            console.log('Enter a valid number. Please try again.');
        }
    }
}

// Starte Bot wenn direkt ausgeführt
if (require.main === module) {
    main();
}
